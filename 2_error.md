# Error Resolution: Duplicate Keys in LazyColumn

## Issue

The app was crashing with `java.lang.IllegalArgumentException: Key "951062953fea434086aa4e2e795e9ed1" was already used` when navigating back from the habit reorder screen to the main home page.

## Root Cause

The HomeScreen.kt had two LazyColumns (main content area and fixed left column) both using the same key (`habitWithCompletions.habit.uuid`) for the same habits. This created duplicate keys in the Compose composition, causing the crash.

## Solution

Fixed by making the keys unique between the two LazyColumns:

- Main content LazyColumn: `"main_${habitWithCompletions.habit.uuid}"`
- Left column LazyColumn: `"left_${habitWithCompletions.habit.uuid}"`

## Files Modified

- `app/src/main/java/com/example/habits9/ui/home/<USER>

## Status

✅ RESOLVED - The duplicate key issue has been fixed by adding unique prefixes to distinguish between the two LazyColumns.
